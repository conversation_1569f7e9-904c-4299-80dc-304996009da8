import os, sys
sys.path.insert(0, '/bi-python-tools')

import pandas as pd
import numpy as np
import Database_Connection_Class as db
import general_tools as gt
import requests
from urllib.request import urlretrieve
from datetime import datetime
import time
import pickle
import json
import math
creds = {'bi_admin':'work011DAY'}
conn = db.DBConnection(mars_cred=creds, 
                         venus_cred=creds,
                         panda_cred=creds,  
                         rs_cred=creds,
                          rs_sslmode = 'verify-ca')
ACCESS_KEY = '********************'
SECRET_KEY = '22ueDKaakUr0O3/w/Sln60v+O7w1EuNL7Nx6PhLv'
from common_functions import *

import logging 
logging.basicConfig(
    filename='/workspace/logs/1d_pull.txt',
    format='%(asctime)s %(levelname)-8s %(message)s',
    level=logging.INFO,
    datefmt='%Y-%m-%d %H:%M:%S')

## this can be done as a airflow job
logging.info('start')
prods = get_prod_aff()
prods = prods.drop_duplicates().reset_index(drop = True)
prods[['cat1', 'cat2', 'cat3', 'cat4', 'cat5', 'cat6', 'newforwardcategory1', 'newforwardcategory2', 'newforwardcategory3', 'newforwardcategory4', 'newforwardcategory5', 'newforwardcategory6', 'subbrand', 'brandcategory']] = prods[['cat1', 'cat2', 'cat3', 'cat4', 'cat5', 'cat6', 'newforwardcategory1', 'newforwardcategory2', 'newforwardcategory3', 'newforwardcategory4', 'newforwardcategory5', 'newforwardcategory6', 'subbrand', 'brandcategory']].replace('', np.nan)
# clean up the cat1-5 columns
def get_cat(row, cat_num = 1):
    cols = ['cat1', 'cat2', 'cat3', 'cat4', 'cat5', 'cat6',  'newforwardcategory1', 'newforwardcategory2', 'newforwardcategory3', 'newforwardcategory4', 'newforwardcategory5', 'newforwardcategory6']
    cat_set = set() 
    for col in cols:
        try:
            text = row[col]
            if pd.notna(text):  # Skip if the value is NaN
                # Split the text and select the part based on cat_num
                cat = text.split(': ')[cat_num - 1]
                cat_set.add(cat)
        except:
            continue
    # for col in ['brandcategory', 'brandcategory2']:
    #     try:
    #         text = row[col]
    #         if pd.notna(text):  # Skip if the value is NaN
    #             # Split the text and select the part based on cat_num
    #             cat = text.split(':')[cat_num - 1]
    #             cat_set.add(cat)
    #     except:
    #         continue
    if (cat_num == 3) & pd.notna(row['subclass2type']):
        if row['subclass2type'] == 'NA':
            cat_set.add('Other')
        else:
            cat_set.add(row['subclass2type'])
    # if (cat_num == 1) & pd.notna(row['subbrand']):
    #     cat_set.add(row['subbrand'])
    return list(cat_set) 
cat1 = prods.apply(get_cat, axis=1)
cat2 = prods.apply(get_cat, cat_num = 2, axis=1)
cat3 = prods.apply(get_cat, cat_num = 3, axis=1)
prods['maincat'] = cat1
prods['subcat1'] = cat2
prods['subcat2'] = cat3
def safe_split(x, delimiter=':', index=1):
    if pd.isna(x) or delimiter not in str(x):
        return x
    try:
        return str(x).split(delimiter)[index]
    except (IndexError, TypeError):
        return x

prods['brandcategory'] = prods['brandcategory'].apply(safe_split)

prods.to_pickle("/workspace/pickle_files/prods.pkl")
logging.info('prods done')

rr_coef = get_coef()
rr_coef.to_pickle('/workspace/pickle_files/rr_coef.pkl')
logging.info('rr coef done')

q = '''select * from bi_work.prod_rr_cust_bucket;'''
prod_rr_cust_bucket = conn.getDfFromRedshift('cust rr bucket', q)
prod_rr_cust_bucket.to_pickle('/workspace/pickle_files/prod_rr_cust_bucket.pkl')

# prod_rr_df = prod_exp_rr()
# prod_rr_df.to_pickle('/workspace/pickle_files/prod_rr_df.pkl')
prod_rr_df_domestic, prod_rr_df_nondomestic = prod_exp_rr()
prod_rr_df_domestic.to_pickle('/workspace/pickle_files/prod_rr_df_domestic.pkl')
prod_rr_df_nondomestic.to_pickle('/workspace/pickle_files/prod_rr_df_nondomestic.pkl')
logging.info('prod_rr_cust done')

# After processing, create dictionaries for faster lookups
subbrand_dict = {}
brandcat_dict = {}

for idx, row in prods.iterrows():
    brand = row['brandname']
    if pd.notna(row['subbrand']):
        if brand not in subbrand_dict:
            subbrand_dict[brand] = set()
        subbrand_dict[brand].add(row['subbrand'])
    
    if pd.notna(row['brandcategory']):
        if brand not in brandcat_dict:
            brandcat_dict[brand] = set()
        brandcat_dict[brand].add(row['brandcategory'])

# Save these dictionaries for quick lookups
with open('/workspace/pickle_files/subbrand_dict.pkl', 'wb') as f:
    pickle.dump(subbrand_dict, f)
    
with open('/workspace/pickle_files/brandcat_dict.pkl', 'wb') as f:
    pickle.dump(brandcat_dict, f)
