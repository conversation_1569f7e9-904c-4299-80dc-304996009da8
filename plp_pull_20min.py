import os, sys
sys.path.insert(0, '/bi-python-tools')

import pandas as pd
import numpy as np
import Database_Connection_Class as db
import general_tools as gt
import requests
from datetime import datetime
import time
import pickle
import json
import math
creds = {'bi_admin':'work011DAY'}
conn = db.DBConnection(mars_cred=creds, 
                         venus_cred=creds,
                         panda_cred=creds,  
                         rs_cred=creds,
                          rs_sslmode = 'verify-ca')
ACCESS_KEY = '********************'
SECRET_KEY = '22ueDKaakUr0O3/w/Sln60v+O7w1EuNL7Nx6PhLv'

from common_functions import *

import logging 
logging.basicConfig(
    filename='/workspace/logs/20min_pull.txt',
    format='%(asctime)s %(levelname)-8s %(message)s',
    level=logging.INFO,
    datefmt='%Y-%m-%d %H:%M:%S')
# this can be done as a cronjob 
logging.info("start")
prods = pd.read_pickle("/workspace/pickle_files/prods.pkl")
# prods = prods.drop_duplicates().reset_index(drop = True)
lettercat = get_lettercat()
standardsizemapping = get_std_mapping()
sa_weight = get_sa_mapping()
boost_factor = get_boost_factor()
rr_coef = pd.read_pickle('/workspace/pickle_files/rr_coef.pkl')
all_size_df = get_inventory_preorder()
df = get_size_availability(prods, all_size_df, standardsizemapping, lettercat, sa_weight)
df = df.merge(boost_factor, how = 'left', on = 'product').fillna({'boost_factor': 1}) 
df[['wo', 'wop', 'sa_weight']] = df[['wo', 'wop', 'sa_weight']].fillna(0)
prod_rr_df_domestic = pd.read_pickle('/workspace/pickle_files/prod_rr_df_domestic.pkl')
prod_rr_df_nondomestic = pd.read_pickle('/workspace/pickle_files/prod_rr_df_nondomestic.pkl')
# Create two separate dataframes for domestic and international
df_dom = df.merge(
    prod_rr_df_domestic, 
    how='left', 
    left_on='product', 
    right_on='productcode'
).fillna({'domestic': 1})

df_intl = df.merge(
    prod_rr_df_nondomestic, 
    how='left', 
    left_on='product', 
    right_on='productcode'
).fillna({'domestic': 0})

# Combine the two dataframes
df = pd.concat([df_dom, df_intl], ignore_index=True)
df = prod_exp_rr_fillna(df)
df = df.reset_index(drop = True)
# df = partial_normalization(df, rr_coef)
df.to_pickle('/workspace/pickle_files/df.pkl')
logging.info("finished sizes")
calib = pd.DataFrame([[0, 'F', 0.9748838582], 
                      [1, 'F', 0.9088820305], 
                      [1, 'M', 0.6025044283], 
                      [0, 'M', 0.5646794586]], columns = ['domestic', 'dept', 'calibration_ratio'])
df_dom = df[(df['domestic'] == 1) | (df['domestic'].isna())].reset_index(drop = True)
df_dom['domestic'] = df_dom['domestic'].fillna(1)
df_intl = df[(df['domestic'] == 0) | (df['domestic'].isna())].reset_index(drop = True)
df_intl['domestic'] = df_intl['domestic'].fillna(0)

shoe_sizes = ['5',
    '5.5',
    '6',
    '6.5',
    '7',
    '7.5',
    '8',
    '8.5',
    '9',
    '9.5',
    '10',
    '10.5',
    '11',
    '11.5',
    '12',
    '12.5',
    '13']
for s in shoe_sizes:
    df_dom[s] = df_dom['size_availability_num2'].apply(lambda sa : (0 if s in sa.split() else 1))
    df_dom['total_' + s] = df_dom['total_size_availability_num2'].apply(lambda sa : (0 if s in sa.split() else 1))
    df_intl[s] = df_intl['size_availability_num2'].apply(lambda sa : (0 if s in sa.split() else 1))
    df_intl['total_' + s] = df_intl['total_size_availability_num2'].apply(lambda sa : (0 if s in sa.split() else 1))
df_dict = {'dom' : df_dom.merge(calib, how = 'left', on = ['domestic','dept']), 
           'intl' : df_intl.merge(calib, how = 'left', on = ['domestic','dept'])}
# product_to_index = {'dom': {product: idx for idx, product in enumerate(df_dict['dom']['product'])},
#                     'intl': {product: idx for idx, product in enumerate(df_dict['intl']['product'])}}
# change readydate to datetime earlier 
df_dict['dom']['readydate'] = pd.to_datetime(df_dict['dom']['readydate'])
df_dict['intl']['readydate'] = pd.to_datetime(df_dict['intl']['readydate'])
shoe_sa_arr = {'dom' : np.array(df_dom[shoe_sizes]), 'intl' : np.array(df_intl[shoe_sizes])}
shoe_total_sa_arr = {'dom' : np.array(df_dom[['total_' + s for s in shoe_sizes]]), 
                     'intl' : np.array(df_intl[['total_' + s for s in shoe_sizes]])}

with open('/search/prod_recs_data/prod_dict.pkl', 'rb') as f:
    prod_dict = pickle.load(f)
with open('/search/prod_recs_data/item_factors.pkl', 'rb') as f:
    prod_emb = pickle.load(f)

dom_embs = []
intl_embs = []
max_num_rows = max(df_dict['dom'].shape[0], df_dict['intl'].shape[0])
for k in range(max_num_rows):
    if k < df_dict['dom'].shape[0]:
        dom_code = df_dict['dom'].iloc[k]['product']
        dom_idx = prod_dict.get(dom_code, -1)
        if dom_idx >= 0:
            dom_embs.append(list(prod_emb[dom_idx]))
        else:
            dom_embs.append(list(np.zeros(shape=prod_emb[0].shape)))
    if k < df_dict['intl'].shape[0]:
        intl_code = df_dict['intl'].iloc[k]['product']
        intl_idx = prod_dict.get(intl_code, -1)
        if intl_idx >= 0:
            intl_embs.append(list(prod_emb[intl_idx]))
        else:
            intl_embs.append(list(np.zeros(shape=prod_emb[0].shape)))
prod_embs = {'dom' : np.array(dom_embs),
            'intl' : np.array(intl_embs)}

size_availability_list = [
    'Full Stock', 'No L', 'No L M', 'No L M S', 'No L M S XS', 'No L M XS', 'No L S',
    'No L S XS', 'No L XS', 'No M', 'No M S', 'No M S XS', 'No M XS', 'No S', 'No S XS',
    'No XL', 'No XL L', 'No XL L M', 'No XL L M S', 'No XL L M XS', 'No XL L S',
    'No XL L S XS', 'No XL L XS', 'No XL M', 'No XL M S', 'No XL M S XS', 'No XL M XS',
    'No XL S', 'No XL S XS', 'No XL XS', 'No XS', 'No XL L M S XS'
]


size_availability_dict = {v: i for i, v in enumerate(size_availability_list)}
size_indices = {'dom' : np.array(df_dom['size_availability2'].map(size_availability_dict).fillna(0)), 
                'intl': np.array(df_intl['size_availability2'].map(size_availability_dict).fillna(0))}
size_indices2 = {'dom' : np.array(df_dom['total_size_availability2'].map(size_availability_dict).fillna(0)),
                 'intl' : np.array(df_intl['total_size_availability2'].map(size_availability_dict).fillna(0))}
catname_dict = {'Clothing' : 1, 'Bottoms' : 2, 'Shoes' : 3, 'Others' : 0}
cat_arr = {'dom' : np.vectorize(catname_dict.__getitem__)(list(df_dom['catname3'])),
           'intl' : np.vectorize(catname_dict.__getitem__)(list(df_intl['catname3']))}
aff_values = {'dom' : df_dom[['full_price', 'markdown', 'lpp', 'hpp', 'HPP+LPP', 'old', 'young', 'Young+Old']].to_numpy(),
              'intl' : df_intl[['full_price', 'markdown', 'lpp', 'hpp', 'HPP+LPP', 'old', 'young', 'Young+Old']].to_numpy()}


# preprocessing clothing
clothing_df_dict = {
    'dom': df_dict['dom'][df_dict['dom']['Clothing']].copy().reset_index(drop = True),
    'intl' : df_dict['intl'][df_dict['intl']['Clothing']].copy().reset_index(drop = True)
}
clothing_size_indices = {'dom' : np.array(clothing_df_dict['dom']['size_availability2'].map(size_availability_dict).fillna(0)), 
                'intl': np.array(clothing_df_dict['intl']['size_availability2'].map(size_availability_dict).fillna(0))}
clothing_size_indices2 = {'dom' : np.array(clothing_df_dict['dom']['total_size_availability2'].map(size_availability_dict).fillna(0)),
                 'intl' : np.array(clothing_df_dict['intl']['total_size_availability2'].map(size_availability_dict).fillna(0))}
clothing_cat_arr = {'dom' : np.vectorize(catname_dict.__getitem__)(list(clothing_df_dict['dom']['catname3'])),
           'intl' : np.vectorize(catname_dict.__getitem__)(list(clothing_df_dict['intl']['catname3']))}
clothing_aff_values = {'dom' : clothing_df_dict['dom'][['full_price', 'markdown', 'lpp', 'hpp', 'HPP+LPP', 'old', 'young', 'Young+Old']].to_numpy(),
              'intl' : clothing_df_dict['intl'][['full_price', 'markdown', 'lpp', 'hpp', 'HPP+LPP', 'old', 'young', 'Young+Old']].to_numpy()}

# precalculate filters 
clothing_dept_idx_dict = {
    'F': {'dom':np.where((clothing_df_dict['dom']['dept'] == 'F')| (clothing_df_dict['dom']['dept'] == 'U'))[0] ,
          'intl': np.where((clothing_df_dict['intl']['dept'] == 'F')| (clothing_df_dict['intl']['dept'] == 'U'))[0] },
    'M': {'dom':np.where((clothing_df_dict['dom']['dept'] == 'M')| (clothing_df_dict['dom']['dept'] == 'U'))[0] ,
          'intl': np.where((clothing_df_dict['intl']['dept'] == 'M')| (clothing_df_dict['intl']['dept'] == 'U'))[0] },
    }
clothing_dept_df_dict = {
    'F': {'dom':clothing_df_dict['dom'][(clothing_df_dict['dom']['dept']== 'F') | (clothing_df_dict['dom']['dept']== 'U')] ,
          'intl': clothing_df_dict['intl'][(clothing_df_dict['intl']['dept']== 'F')| (clothing_df_dict['intl']['dept']== 'U')]},
    'M': {'dom':clothing_df_dict['dom'][(clothing_df_dict['dom']['dept']== 'M')| (clothing_df_dict['dom']['dept']== 'U')] ,
          'intl': clothing_df_dict['intl'][(clothing_df_dict['intl']['dept']== 'M')| (clothing_df_dict['intl']['dept']== 'U')]},
}

from sklearn.preprocessing import MultiLabelBinarizer
mlb_maincat_dom = MultiLabelBinarizer()
mlb_subcat1_dom = MultiLabelBinarizer()
mlb_subcat2_dom = MultiLabelBinarizer()

maincat_sparse_dom = mlb_maincat_dom.fit_transform(df_dict['dom']['maincat'])
subcat1_sparse_dom = mlb_subcat1_dom.fit_transform(df_dict['dom']['subcat1'])
subcat2_sparse_dom = mlb_subcat2_dom.fit_transform(df_dict['dom']['subcat2'])


mlb_maincat_intl = MultiLabelBinarizer()
mlb_subcat1_intl = MultiLabelBinarizer()
mlb_subcat2_intl = MultiLabelBinarizer()

maincat_sparse_intl = mlb_maincat_intl.fit_transform(df_dict['intl']['maincat'])
subcat1_sparse_intl = mlb_subcat1_intl.fit_transform(df_dict['intl']['subcat1'])
subcat2_sparse_intl = mlb_subcat2_intl.fit_transform(df_dict['intl']['subcat2'])

with open("/workspace/pickle_files/mlb_and_sparse.pkl", "wb") as f:
    pickle.dump({
        'mlb_maincat': {'dom': mlb_maincat_dom, 'intl': mlb_maincat_intl},
        'maincat_sparse': {'dom': maincat_sparse_dom, 'intl': maincat_sparse_intl},
        'mlb_subcat1': {'dom': mlb_subcat1_dom, 'intl': mlb_subcat1_intl},
        'subcat1_sparse': {'dom': subcat1_sparse_dom, 'intl': subcat1_sparse_intl},
        'mlb_subcat2': {'dom': mlb_subcat2_dom, 'intl': mlb_subcat2_intl},
        'subcat2_sparse': {'dom': subcat2_sparse_dom, 'intl': subcat2_sparse_intl}
    }, f)

#df_dom.to_pickle('/workspace/pickle_files/df_dom.pkl')
#df_intl.to_pickle('/workspace/pickle_files/df_intl.pkl')
with open('/workspace/pickle_files/df_dict.pkl', 'wb') as handle:
    pickle.dump(df_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
# with open('/workspace/pickle_files/product_to_index', 'wb') as handle:
#     pickle.dump(product_to_index, handle, protocol=pickle.HIGHEST_PROTOCOL)
with open('/workspace/pickle_files/shoe_sa_arr.pkl', 'wb') as handle:
    pickle.dump(shoe_sa_arr, handle, protocol=pickle.HIGHEST_PROTOCOL)
with open('/workspace/pickle_files/shoe_total_sa_arr.pkl', 'wb') as handle:
    pickle.dump(shoe_total_sa_arr, handle, protocol=pickle.HIGHEST_PROTOCOL)
with open('/workspace/pickle_files/size_indices.pkl', 'wb') as handle:
    pickle.dump(size_indices, handle, protocol=pickle.HIGHEST_PROTOCOL)
with open('/workspace/pickle_files/size_indices2.pkl', 'wb') as handle:
    pickle.dump(size_indices2, handle, protocol=pickle.HIGHEST_PROTOCOL)
with open('/workspace/pickle_files/cat_arr.pkl', 'wb') as handle:
    pickle.dump(cat_arr, handle, protocol=pickle.HIGHEST_PROTOCOL)
with open('/workspace/pickle_files/aff_values.pkl', 'wb') as handle:
    pickle.dump(aff_values, handle, protocol=pickle.HIGHEST_PROTOCOL)

with open('/workspace/pickle_files/clothing_df_dict.pkl', 'wb') as handle:
    pickle.dump(clothing_df_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
with open('/workspace/pickle_files/clothing_size_indices.pkl', 'wb') as handle:
    pickle.dump(clothing_size_indices, handle, protocol=pickle.HIGHEST_PROTOCOL)
with open('/workspace/pickle_files/clothing_size_indices2.pkl', 'wb') as handle:
    pickle.dump(clothing_size_indices2, handle, protocol=pickle.HIGHEST_PROTOCOL)
with open('/workspace/pickle_files/clothing_cat_arr.pkl', 'wb') as handle:
    pickle.dump(clothing_cat_arr, handle, protocol=pickle.HIGHEST_PROTOCOL)
with open('/workspace/pickle_files/clothing_aff_values.pkl', 'wb') as handle:
    pickle.dump(clothing_aff_values, handle, protocol=pickle.HIGHEST_PROTOCOL)
with open('/workspace/pickle_files/clothing_dept_df_dict.pkl', 'wb') as handle:
    pickle.dump(clothing_dept_df_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
with open('/workspace/pickle_files/clothing_dept_idx_dict.pkl', 'wb') as handle:
    pickle.dump(clothing_dept_idx_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

with open('/workspace/pickle_files/prod_embs.pkl', 'wb') as handle:
    pickle.dump(prod_embs, handle, protocol=pickle.HIGHEST_PROTOCOL)


log_file_path = '/search/prod_recs_update.log'
with open(log_file_path, 'r') as file:
    log_date_str = file.readline().strip().split()[0]
log_date = datetime.strptime(log_date_str, '%Y/%m/%d').date().strftime('%Y%m%d')
with open('/workspace/pickle_files/prod_emb_date.pkl', 'wb') as handle:
    pickle.dump(log_date, handle, protocol=pickle.HIGHEST_PROTOCOL)


logging.info("finished everything")