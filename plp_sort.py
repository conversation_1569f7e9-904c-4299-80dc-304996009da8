import os, sys
sys.path.insert(0, '/bi-python-tools')
sys.path.append('/workspace')
os.environ["OMP_NUM_THREADS"] = "3"
os.environ["NUMEXPR_NUM_THREADS"] = "3"
os.environ["MKL_NUM_THREADS"] = "3"
os.environ['TZ'] = 'America/Los_Angeles'

import pandas as pd
import numpy as np
import Database_Connection_Class as db
import general_tools as gt
import requests
from urllib.request import urlretrieve
from urllib.parse import unquote
from datetime import datetime
import time
import pickle
import json
import math
creds = {'bi_admin':'work011DAY'}
conn = db.DBConnection(mars_cred=creds, 
                         venus_cred=creds,
                         panda_cred=creds,  
                         rs_cred=creds,
                          rs_sslmode = 'verify-ca')
ACCESS_KEY = '********************'
SECRET_KEY = '22ueDKaakUr0O3/w/Sln60v+O7w1EuNL7Nx6PhLv'
from common_functions import *
import gunicorn
import uvicorn
import logging 
logging.basicConfig(
    filename='/workspace/logs/plp_sort_log.txt',
    format='%(asctime)s %(levelname)-8s %(message)s',
    level=logging.INFO,
    datefmt='%Y-%m-%d %H:%M:%S')
logger = logging.getLogger("uvicorn.error")

from fastapi import FastAPI, Request, Query, HTTPException
from fastapi.responses import PlainTextResponse


###########################
### Every 20min Pull ###
###########################

with open('/workspace/pickle_files/prod_emb_date.pkl', 'rb') as handle:
    prod_emb_date = pickle.load(handle)

default_pw = {'lv': '1970-1-1',
 'ps_clo': {'No L': '0',
  'No L M': '0',
  'No L M S': '0',
  'No L M S XS': '0',
  'No L M XS': '0',
  'No L S': '0',
  'No L S XS': '0',
  'No L XS': '0',
  'No M': '0',
  'No M S': '0',
  'No M S XS': '0',
  'No M XS': '0',
  'No S': '0',
  'No S XS': '0',
  'No XL': '0',
  'No XL L': '0',
  'No XL L M': '0',
  'No XL L M S': '0',
  'No XL L M XS': '0',
  'No XL L S': '0',
  'No XL L S XS': '0',
  'No XL L XS': '0',
  'No XL M': '0',
  'No XL M S': '0',
  'No XL M S XS': '0',
  'No XL M XS': '0',
  'No XL S': '0',
  'No XL S XS': '0',
  'No XL XS': '0',
  'No XS': '0',
  'No XL L M S XS': '0'},
 'ps_bot': {'No L': '0',
  'No L M': '0',
  'No L M S': '0',
  'No L M S XS': '0',
  'No L M XS': '0',
  'No L S': '0',
  'No L S XS': '0',
  'No L XS': '0',
  'No M': '0',
  'No M S': '0',
  'No M S XS': '0',
  'No M XS': '0',
  'No S': '0',
  'No S XS': '0',
  'No XL': '0',
  'No XL L': '0',
  'No XL L M': '0',
  'No XL L M S': '0',
  'No XL L M XS': '0',
  'No XL L S': '0',
  'No XL L S XS': '0',
  'No XL L XS': '0',
  'No XL M': '0',
  'No XL M S': '0',
  'No XL M S XS': '0',
  'No XL M XS': '0',
  'No XL S': '0',
  'No XL S XS': '0',
  'No XL XS': '0',
  'No XS': '0',
  'No XL L M S XS': '0'},
 'ps_sho': {'No 5': '0',
  'No 5.5': '0',
  'No 6': '0',
  'No 6.5': '0',
  'No 7': '0',
  'No 7.5': '0',
  'No 8': '0',
  'No 8.5': '0',
  'No 9': '0',
  'No 9.5': '0',
  'No 10': '0',
  'No 10.5': '0',
  'No 11': '0',
  'No 11.5': '0',
  'No 12': '0',
  'No 12.5': '0',
  'No 13': '0'},
 'ps_aff': {'FP': '1',
  'MD': '1',
  'HPP': '1',
  'LPP': '1',
  'Young': '1',
  'Old': '1',
  'Young+Old': '1',
  'FemmeGirl': '1',
  'CoolGirl': '1',
  'SophisticatedChic': '1',
  'Old2': '1',
  'Young2': '1',
  'RR': '0.4'}, # check avg/median rr 
 'domestic': 1, 
 'visitorsegment': '',
 'new_cust': 1}
size_availability_list = [
    'Full Stock', 'No L', 'No L M', 'No L M S', 'No L M S XS', 'No L M XS', 'No L S',
    'No L S XS', 'No L XS', 'No M', 'No M S', 'No M S XS', 'No M XS', 'No S', 'No S XS',
    'No XL', 'No XL L', 'No XL L M', 'No XL L M S', 'No XL L M XS', 'No XL L S',
    'No XL L S XS', 'No XL L XS', 'No XL M', 'No XL M S', 'No XL M S XS', 'No XL M XS',
    'No XL S', 'No XL S XS', 'No XL XS', 'No XS', 'No XL L M S XS'
]
size_availability_dict = {v: i for i, v in enumerate(size_availability_list)}
shoe_sa_list = ['No 5',
'No 5.5',
'No 6',
'No 6.5',
'No 7',
'No 7.5',
'No 8',
'No 8.5',
'No 9',
'No 9.5',
'No 10',
'No 10.5',
'No 11',
'No 11.5',
'No 12',
'No 12.5',
'No 13']
shoe_sa_dict = {size: idx for idx, size in enumerate(shoe_sa_list)}  
type_idx_dict = {'clo' : 1, 'bot' : 2, 'sho' : 3}
catname_dict = {'Clothing' : 1, 'Bottoms' : 2, 'Shoes' : 3, 'Others' : 0}

rr_coef = pd.read_pickle('/workspace/pickle_files/rr_coef.pkl')
with open('/workspace/pickle_files/df_dict.pkl', 'rb') as handle:
    df_dict = pickle.load(handle)
# with open('/workspace/pickle_files/product_to_index', 'rb') as handle:
#     product_to_index = pickle.load(handle)
with open('/workspace/pickle_files/shoe_sa_arr.pkl', 'rb') as handle:
    shoe_sa_arr = pickle.load(handle)
with open('/workspace/pickle_files/shoe_total_sa_arr.pkl', 'rb') as handle:
    shoe_total_sa_arr = pickle.load(handle)
with open('/workspace/pickle_files/size_indices.pkl', 'rb') as handle:
    size_indices = pickle.load(handle)
with open('/workspace/pickle_files/size_indices2.pkl', 'rb') as handle:
    size_indices2 = pickle.load(handle)
with open('/workspace/pickle_files/shoe_sa_arr.pkl', 'rb') as handle:
    shoe_sa_arr = pickle.load(handle)
with open('/workspace/pickle_files/shoe_total_sa_arr.pkl', 'rb') as handle:
    shoe_total_sa_arr = pickle.load(handle)
with open('/workspace/pickle_files/size_indices.pkl', 'rb') as handle:
    size_indices = pickle.load(handle)
with open('/workspace/pickle_files/size_indices2.pkl', 'rb') as handle:
    size_indices2 = pickle.load(handle)
with open('/workspace/pickle_files/size_indices2.pkl', 'rb') as handle:
    size_indices2 = pickle.load(handle)
with open('/workspace/pickle_files/cat_arr.pkl', 'rb') as handle:
    cat_arr = pickle.load(handle)
with open('/workspace/pickle_files/aff_values.pkl', 'rb') as handle:
    aff_values = pickle.load(handle)

with open('/workspace/pickle_files/clothing_df_dict.pkl', 'rb') as handle:
    clothing_df_dict = pickle.load(handle)
with open('/workspace/pickle_files/clothing_size_indices.pkl', 'rb') as handle:
    clothing_size_indices = pickle.load(handle)
with open('/workspace/pickle_files/clothing_size_indices2.pkl', 'rb') as handle:
    clothing_size_indices2 = pickle.load(handle)
with open('/workspace/pickle_files/clothing_cat_arr.pkl', 'rb') as handle:
    clothing_cat_arr = pickle.load(handle)
with open('/workspace/pickle_files/clothing_aff_values.pkl', 'rb') as handle:
    clothing_aff_values = pickle.load(handle)
with open('/workspace/pickle_files/clothing_dept_df_dict.pkl', 'rb') as handle:
    clothing_dept_df_dict = pickle.load(handle)
with open('/workspace/pickle_files/clothing_dept_idx_dict.pkl', 'rb') as handle:
    clothing_dept_idx_dict = pickle.load(handle)

with open('/workspace/pickle_files/prod_embs.pkl', 'rb') as f:
    prod_embs = pickle.load(f)

with open('/workspace/pickle_files/prod_rr_cust_bucket.pkl', 'rb') as f:
    prod_rr_cust_bucket = pickle.load(f)

with open("/workspace/pickle_files/mlb_and_sparse.pkl", "rb") as f:
    mlb_and_sparse = pickle.load(f)

try:
    with open('/workspace/pickle_files/subbrand_dict.pkl', 'rb') as f:
        subbrand_dict = pickle.load(f)
    with open('/workspace/pickle_files/brandcat_dict.pkl', 'rb') as f:
        brandcat_dict = pickle.load(f)
    logging.info("Loaded subbrand and brandcat dictionaries")
except Exception as e:
    logging.warning(f"Could not load subbrand or brandcat dictionaries: {e}")
    subbrand_dict = {}
    brandcat_dict = {}

# remove unnecessary columns  
columns = ['product','Clothing','dept','catname1','brandname','capped_netrev_on_median_imp','finalsale', 'readydate','proj_kr',
           'sa_weight','calibration_ratio','boost_factor', 'maincat', 'subcat1', 'subcat2', 'subbrand', 'brandcategory', 
           'cust_rr_units_bucket_new',
           'cust_rr_units_bucket_0', 'cust_rr_units_bucket_0_001',
            'cust_rr_units_bucket_0_3', 'cust_rr_units_bucket_0_399',
            'cust_rr_units_bucket_0_477', 'cust_rr_units_bucket_0_597',
            'cust_rr_units_bucket_0_711', 'cust_rr_units_bucket_0_825']
df_dict['dom'] = df_dict['dom'].reset_index(drop = True)[columns]
df_dict['intl'] = df_dict['intl'].reset_index(drop = True)[columns]

#################
### real time ###
#################

def get_sort(cat_arr, size_indices, size_indices2, shoe_sa_arr, shoe_total_sa_arr,aff_values, browserid = 'none', dept = 'F', cat = None, subcat1 = None, subcat2 = None, brand = None, siteflag = 'R', rr = True,  num_prec_elig = 100, prec_share = 0.15, prec_min_rpi_quantile = 0.5):
    
    # get customer's affinities
    try:
        pw = get_affinity(browserid, siteflag)
    except: 
        pw = default_pw
    if pw is None or len(pw) == 0:
        pw = default_pw
    else: # only a subset of the fields are empty
        for key in ['clo', 'bot', 'sho']:
            if not pw[f'ps_{key}']:
                pw[f'ps_{key}'] = default_pw[f'ps_{key}']
        if not pw['ps_aff']:
            pw['ps_aff'] = default_pw['ps_aff']
            pw['new_cust'] = 1 
        else:
            for key in ['FP', 'MD', 'HPP', 'LPP', 'Young', 'Old', 'Young+Old', 'FemmeGirl', 'CoolGirl', 'SophisticatedChic', 'Old2', 'Young2']:
                if not pw['ps_aff'][key]:
                    pw['ps_aff'][key] = default_pw['aff'][key]
            if not pw['ps_aff']['RR']:
                pw['ps_aff']['RR'] = default_pw['aff']['RR']
                pw['new_cust'] = 1 # if no RR, new customer
            else:
                pw['new_cust'] = 0 # else, existing customer

    try:
        dom = pw['domestic']
    except KeyError:
        dom = 1
    if dom == 1:
        dom = 'dom'
    else:
        dom = 'intl'
    
    
    
    # Apply conditions to mask
    if cat == 'clothing':  # Case 1: Clothing category
        filter_idx = clothing_dept_idx_dict[dept][dom]
        df2 = clothing_dept_df_dict[dept][dom]
    elif cat:  # Case 2: Non-clothing category specified
        dept_mask = ((df_dict[dom]['dept'] == dept) | (df_dict[dom]['dept'] == 'U')) if dept else True
        try:
            cat_idx = mlb_and_sparse['mlb_maincat'][dom].classes_.tolist().index(cat)
            cat_mask = mlb_and_sparse['maincat_sparse'][dom][:, cat_idx] == 1
        except ValueError:
            # FALLBACK: Try using subbrand as fallback for cat
            if brand and brand in subbrand_dict:
                logging.info(f"Category '{cat}' not found in classes. Checking subbrand fallback.")
                # Actually filter by subbrand field
                cat_mask = (df_dict[dom]['brandname'] == brand) & (df_dict[dom]['subbrand'] == cat)
                if cat_mask.any():
                    logging.info(f"Found '{cat}' in subbrand field for {brand}. Using these products.")
                else:
                    logging.info(f"'{cat}' not found in subbrand field for {brand}.")
                    cat_mask = np.zeros(len(df_dict[dom]), dtype=bool)
            else:
                # No brand or brand not in dict, return empty mask
                logging.info(f"Category '{cat}' not found and no valid brand for fallback.")
                cat_mask = np.zeros(len(df_dict[dom]), dtype=bool)
                
        if subcat1:
            try:
                subcat1_idx = mlb_and_sparse['mlb_subcat1'][dom].classes_.tolist().index(subcat1)
                subcat1_mask = mlb_and_sparse['subcat1_sparse'][dom][:, subcat1_idx] == 1
            except ValueError:
                # FALLBACK: Try using brandcategory as fallback for subcat1
                if brand and brand in brandcat_dict:
                    logging.info(f"subcat1 '{subcat1}' not found in classes. Checking brandcategory fallback.")
                    # Actually filter by brandcategory field
                    subcat1_mask = (df_dict[dom]['brandname'] == brand) & (df_dict[dom]['brandcategory'] == subcat1)
                    if subcat1_mask.any():
                        logging.info(f"Found '{subcat1}' in brandcategory field for {brand}. Using these products.")
                    else:
                        logging.info(f"'{subcat1}' not found in brandcategory field for {brand}.")
                        subcat1_mask = np.zeros(len(df_dict[dom]), dtype=bool)
                else:
                    # No brand or brand not in dict, return empty mask
                    logging.info(f"subcat1 '{subcat1}' not found and no valid brand for fallback.")
                    subcat1_mask = np.zeros(len(df_dict[dom]), dtype=bool)
        else:
            subcat1_mask = True
        if subcat2:
            subcat2_idx = mlb_and_sparse['mlb_subcat2'][dom].classes_.tolist().index(subcat2)
            subcat2_mask = mlb_and_sparse['subcat2_sparse'][dom][:, subcat2_idx] == 1
        else:
            subcat2_mask = True
        brand_mask = (df_dict[dom]['brandname'] == brand) if brand else True
        combined_mask = dept_mask & cat_mask & brand_mask & subcat1_mask & subcat2_mask
        filter_idx = np.where(combined_mask)[0]
        df2 = df_dict[dom][combined_mask]
    else:  # Case 3: No category specified (including brand-only queries)
        dept_mask = ((df_dict[dom]['dept'] == dept) | (df_dict[dom]['dept'] == 'U')) if dept else True
        brand_mask = (df_dict[dom]['brandname'] == brand) if brand else True
        combined_mask = dept_mask & brand_mask
        filter_idx = np.where(combined_mask)[0]
        df2 = df_dict[dom][combined_mask]
    
    if len(df2) == 0:
        logging.info(f'No products found for filters: cat={cat}, subcat1={subcat1}, subcat2={subcat2}, brand={brand}')
        return ''
    
    # Personalized RR Calc
    if pw['new_cust'] == 1:
        cust_rr_units_bucket = 'new'
    else:
        cust_rr_units = float(pw['ps_aff']['RR'])
        if cust_rr_units == 1:
            cust_rr_units_bucket = 0.825
        else:
            cust_rr_units_bucket = prod_rr_cust_bucket[
                    (prod_rr_cust_bucket["lowerbound_ge"] <= cust_rr_units) &
                    (prod_rr_cust_bucket["higherbound_lt"] > cust_rr_units)
                ]['cust_rr_units_bucket'].values[0]
    bucket_colname = 'cust_rr_units_bucket_'+ str(cust_rr_units_bucket).replace('.', '_')
    # df2 = df2[df2['cust_rr_units_bucket'] == cust_rr_units_bucket] - instead of slicing a dataframe, just save the indices as a numpy array
    
    # sum_of_features_coefficients = df2['pre_load_rr_features'] + rr_coef.iloc[0, 2] * ( (cust_rr_units - df2['cust_rr_units_mean']) / df2['cust_rr_units_std'] )
    # adjusted_proj_rr = np.array(( 1 / (1 + np.power(math.e, -sum_of_features_coefficients)) ) * df2['calibration_ratio'])
    adjusted_proj_rr = df2[bucket_colname]
    finalsale_idx = np.where(np.array(df2['finalsale']) == 1)[0]
    adjusted_proj_rr.iloc[finalsale_idx] = 0.
    
    # Size availability / affinity calculations
    sa_key_arr = 100 * cat_arr[dom][filter_idx] + size_indices[dom][filter_idx]
    sa_key_arr2 = 100 * cat_arr[dom][filter_idx] + size_indices2[dom][filter_idx]
    loop_list = ['clo', 'bot', 'oth']
    if cat != 'clothing':
        shoes_aff_vec = np.array([float(s) for s in pw['ps_sho'].values()])
        shoe_aff_1 = np.matmul(shoe_sa_arr[dom][filter_idx] , shoes_aff_vec)
        shoe_aff_2 = np.matmul(shoe_total_sa_arr[dom][filter_idx], shoes_aff_vec)
        shoe_aff_scores = shoe_aff_1 + .6 * (shoe_aff_2 - shoe_aff_1)
        loop_list.append('sho')
    ps_dict = {}
    # for item_type in ['clo', 'bot', 'sho', 'oth']:
    for item_type in loop_list:

        cat_code = 100 * type_idx_dict.get(item_type, 0)
        if item_type != 'oth':
            # # added default fallback per category
            # if not pw[f'ps_{item_type}']:
            #     pw[f'ps_{item_type}'] = default_pw[pw[f'ps_{item_type}']]
            w_dict = pw[f'ps_{item_type}']
            for key in w_dict.keys():
                if (item_type == 'sho') & (cat != 'clothing'):
                    size_code = shoe_sa_dict.get(key, -1)  # Use the dictionary for lookup
                else:
                    size_code = size_availability_list.index(key)

                if size_code != -1:  # Ensure the size_code is valid
                    ps_dict[cat_code + size_code] = float(w_dict[key])
            for size_code in range(32):
                if cat_code + size_code not in ps_dict.keys():
                    ps_dict[cat_code + size_code] = 0
        else:
            for size_code in range(32):
                ps_dict[cat_code + size_code] = 0

    psa = np.vectorize(ps_dict.__getitem__)(sa_key_arr)
    psa2 = np.vectorize(ps_dict.__getitem__)(sa_key_arr2)
    nonshoes_aff_scores = psa + (psa2 - psa) * .6
    nonshoes_aff_scores = nonshoes_aff_scores
    
    if cat != 'clothing':
        w_sa_final = df2['sa_weight'] + nonshoes_aff_scores + shoe_aff_scores
    else:
        w_sa_final = df2['sa_weight'] + nonshoes_aff_scores
    
    # Other affinities
    aff_list = ['FP', 'MD','LPP','HPP', 'HPP+LPP', 'Old', 'Young', 'Young+Old']
    aff_mat = np.ones(len(aff_list))
    if pw['ps_aff']:
        for aff, weight in pw['ps_aff'].items():
            if aff in aff_list:
                aff_mat[aff_list.index(aff)] = weight 
    
    values = aff_values[dom][filter_idx]    
    w_aff_md = values[:, :2]@aff_mat[:2].T
    w_aff_lpp = values[:, 2:5]@aff_mat[2:5].T
    w_aff_age = values[:, 5:8]@aff_mat[5:8].T

    if pw['lv'] is None or pw['lv'] == 'None':
        new_prods = np.ones(df2.shape[0])
    else:
        new_prods = (df2['readydate']>=pd.to_datetime(pw['lv'])).astype(float) 
    if pw['visitorsegment'] == 'F':
        const = 0.1
    else:
        const = 0.25    
    w_prods_new = new_prods + const * (1-new_prods)
    
    # Calculate final combined score
    final_weight = (w_sa_final 
                       * w_aff_md
                       * w_aff_lpp
                       * w_aff_age
                       * w_prods_new
                       * df2['boost_factor']
                      )
    if rr:
        final_weight = final_weight / df2['proj_kr'] * (1 - adjusted_proj_rr) # prob(keep)
    final_rpi_score = np.array(df2['capped_netrev_on_median_imp'] * final_weight)
    
    prods = np.array(df2['product'])
    res_idx = np.argsort(-final_rpi_score) 
    num_prec_elig = min(num_prec_elig, len(df2))
    augmented_res_idx = res_idx
    # if num_prec_elig * prec_share >= 1:
    #     min_rpi = df2['capped_netrev_on_median_imp'].quantile(prec_min_rpi_quantile)
    #     inelig_idx = np.where(final_rpi_score < min_rpi)[0]
    #     redis_user_factors = raw_redis_db.get(f'pr_emb_{browserid}_{prod_emb_date}')
    #     if redis_user_factors:
    #         user_factors = np.frombuffer(redis_user_factors, dtype=np.float16)
    #         prod_rec_score = np.matmul(prod_embs[dom], user_factors)[filter_idx]
    #         prod_rec_score = 100 * (prod_rec_score - prod_rec_score.min())
    #         prod_rec_score[inelig_idx] = 0
    #         #prod_rec_score = (prod_rec_score
    #         #               / df2['proj_kr'] * (1 - adjusted_proj_rr) # prob(keep)
    #         #              )
    #         prec_spots = np.where(np.random.uniform(0,1,num_prec_elig) <= prec_share)[0]
    #         num_prec_spots = len(prec_spots)
    #         nonprec_spots = np.append(np.array(list(set(range(num_prec_elig)) - set(prec_spots))),
    #                                 np.array(list(range(num_prec_elig,len(final_rpi_score)+num_prec_spots))))
        
    #         prec_res_idx = np.argsort(-prod_rec_score)[:num_prec_spots]
        
    #         augmented_res_idx = np.zeros(shape = len(final_rpi_score) + num_prec_spots, dtype = int)
    #         augmented_res_idx[prec_spots] = prec_res_idx
    #         augmented_res_idx[nonprec_spots] = res_idx
        
    #         res_idx = pd.Series(augmented_res_idx).drop_duplicates().to_list()

    # For frequent users: Insert ATC -> hearted/wishlisted -> viewed PDP items
    if pw['visitorsegment'] == 'F':
        insert_positions = [1, 3, 5, 7, 9]

        atc_hearted_viewed_items = pw['atchv'].split(',')  # List of additional product indices to insert
        # check products that sufficies the instock condition and filter
        # below method doesnt reserve the order of atc items 
        # intersection_indices = np.where(df_dict[dom]['product'].isin(set(atc_hearted_viewed_items)))[0]
        # final_indices = np.intersect1d(intersection_indices, res_idx) 
        product_to_index = {product: idx for idx, product in enumerate(prods)}
        final_indices = [
                product_to_index[product]
                for product in atc_hearted_viewed_items
                if product in product_to_index
            ]
        if len(final_indices) > 0:
            additional_products = final_indices[:min(5, len(final_indices))]

            # Create a mask for insertion positions
            insert_mask = np.zeros(len(augmented_res_idx) + len(additional_products), dtype=bool)
            insert_mask[np.array(insert_positions[:len(additional_products)])] = True

            # Create the final augmented index array
            new_augmented_res_idx = np.zeros(len(augmented_res_idx) + len(additional_products), dtype=int)

            # Fill in products at insertion positions
            new_augmented_res_idx[insert_mask] = additional_products

            # Fill remaining positions
            remaining_positions = ~insert_mask
            new_augmented_res_idx[remaining_positions] = augmented_res_idx[:remaining_positions.sum()]

            # Deduplicate and finalize
            res_idx = pd.Series(new_augmented_res_idx).drop_duplicates().to_list()
    
    res = prods[res_idx].tolist()
    results = ','.join(res)
    return results

app = FastAPI()
@app.on_event("startup")
async def startup_event():
    logger.info("FastAPI app started")

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("FastAPI app shutdown")
    
@app.get("/plp_sort/", response_class = PlainTextResponse)
def home_page(cat: str = Query(None, alias="Category"), 
              subcat1: str = Query(None, alias="Subcat1"), 
              subcat2: str = Query(None, alias="Subcat2"), 
              brand: str = Query(None, alias="Brand"),
              dept: str = Query(None, alias="Dept"),
              browserid: str = Query(None, alias='BrowserID'),
              rr: str = Query(None, alias='RR')):
    # Before the title() conversion, check if the string contains encoded characters
    if cat and ('%' in cat or '+' in cat):
        cat = cat.replace('+', ' ')
        # Try to decode first - change %26 to &
        cat = unquote(cat)
        logging.info(f"Decoded category: {cat}")
    if subcat1 and ('%' in subcat1 or '+' in subcat1):
        subcat1 = subcat1.replace('+', ' ')
        # Try to decode first - change %26 to &
        subcat1 = unquote(subcat1)
        logging.info(f"Decoded subcategory 1: {subcat1}")
    if subcat2 and ('%' in subcat2 or '+' in cat):
        subcat2 = subcat2.replace('+', ' ')
        # Try to decode first - change %26 to &
        subcat2 = unquote(subcat2)
        logging.info(f"Decoded subcategory 2: {subcat2}")
    if brand and ('%' in brand or '+' in brand):
        brand = brand.replace('+', ' ')
        # Try to decode first - change %26 to &
        brand = unquote(brand)
    # Convert inputs to proper case or handle them as case-insensitive
    cat = cat.lower() if cat else None
    subcat1 = subcat1.lower() if subcat1 else None
    subcat2 = subcat2.lower() if subcat2 else None
    brand = brand.lower() if brand else brand
    browserid = browserid if browserid else browserid
    dept = dept.upper() if dept else 'F'
    logging.info("category: %s, subcat1: %s, subcat2: %s, brand: %s, browserid: %s, dept: %s",
            cat or "N/A", subcat1 or "N/A", subcat2 or "N/A", brand or "N/A", browserid or "N/A", dept or "N/A")
    
    if not rr:
        rr = True
    elif rr == 'False':
        rr = False
    try:
        if cat != 'clothing':
            results = get_sort(cat_arr, size_indices, size_indices2, shoe_sa_arr, shoe_total_sa_arr,aff_values, browserid, dept, cat, subcat1, subcat2, brand, rr= rr)
        else:
            results = get_sort(clothing_cat_arr, clothing_size_indices, clothing_size_indices2, shoe_sa_arr, shoe_total_sa_arr,clothing_aff_values, browserid, dept, cat, subcat1, subcat2, brand, rr= rr)
        logging.info(f'finished sort.')
        return results
    except Exception as e:
        logger.exception(f"Error occurred during sorting: {e}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")
