#!/usr/bin/env python3
import os
import re
import datetime
from datetime import datetime, timedelta
import time
import tempfile
import sys
import pytz  # ✅ use pytz for timezone handling (no extra install needed)

# Ensure your email_tools can be found
sys.path.insert(0, '/bi-python-tools')
sys.path.insert(0, '/workspace/bi-python-tools')
import email_tools

# ----------------- CONFIG -----------------
SENDER = "<EMAIL>"
RECEIVERS = ["<EMAIL>"]

# Pacific Timezone via pytz
TZ = pytz.timezone("America/Los_Angeles")

# Logs
PLP_1D_LOG = "/workspace/logs/plp_1d.log"
PLP_20MIN_LOG = "/workspace/logs/plp_20min.log"
PLP_SORT_LOG = "/workspace/logs/plp_sort_log.txt"
TAIL_LINES_EAGER = 1000
SORT_TAIL_LINES = 100
SORT_LOOKBACK_MINUTES = 20

# Error patterns
ERROR_PATTERNS = [
    r'(?i)\berror\b',
    r'(?i)\bexception\b',
    r'(?i)traceback',
    r'(?i)\bfailed\b',
    r'(?i)\bcritical\b',
]

# Pickle freshness
PICKLE_DIR = "/workspace/pickle_files"
PICKLE_FILES_20min = ('prod_emb_date.pkl',
 'prod_embs.pkl',
 'clothing_dept_idx_dict.pkl',
 'clothing_dept_df_dict.pkl',
 'clothing_aff_values.pkl',
 'clothing_cat_arr.pkl',
 'clothing_size_indices2.pkl',
 'clothing_size_indices.pkl',
 'clothing_df_dict.pkl',
 'aff_values.pkl',
 'cat_arr.pkl',
 'size_indices2.pkl',
 'size_indices.pkl',
 'shoe_total_sa_arr.pkl',
 'shoe_sa_arr.pkl',
 'mlb_and_sparse.pkl',
 'df_dict.pkl')

PICKLE_FILES_1d = ('prods.pkl',
 'brandcat_dict.pkl',
 'subbrand_dict.pkl',
 'prod_rr_df_nondomestic.pkl',
 'prod_rr_df_domestic.pkl',
 'prod_rr_cust_bucket.pkl',
 'rr_coef.pkl')  # single-element tuple needs comma
PICKLE_FRESH_MINUTES_20min = 60
PICKLE_FRESH_MINUTES_1d = 24*60
# ------------------------------------------


def _tail_lines(path, n):
    try:
        with open(path, "r", errors="replace") as f:
            return f.readlines()[-n:]
    except Exception:
        return []


def _file_exists_nonempty(path):
    return os.path.isfile(path) and os.path.getsize(path) > 0


def _has_error(line):
    return any(re.search(p, line) for p in ERROR_PATTERNS)


def _scan_simple_file(path, tail_n):
    lines = _tail_lines(path, tail_n)
    hits = []
    for i, line in enumerate(lines, 1):
        if _has_error(line):
            hits.append(f"Line ~{i}: {line.strip()}")
    return hits


TS_FORMATS = [
    "%Y-%m-%d %H:%M:%S",
    "%Y-%m-%d %H:%M:%S,%f",
    "%Y-%m-%d %H:%M:%S.%f",
    "%Y-%m-%dT%H:%M:%S",
    "%Y-%m-%dT%H:%M:%S.%f",
]

def _parse_ts_from_line(line):
    """
    Try to parse a timestamp at the beginning of the line using known formats.
    If parse succeeds, assume the timestamp is Pacific local time and localize it.
    """
    s = line.strip()
    parts = s.split()
    candidates = [s[:26], s[:23], s[:19]]
    if parts:
        candidates.append(parts[0] + (f" {parts[1]}" if len(parts) > 1 else ""))
    for cand in (c.strip() for c in candidates if c):
        for fmt in TS_FORMATS:
            try:
                dt_naive = datetime.strptime(cand, fmt)  # naive
                return TZ.localize(dt_naive)             # ✅ localize to Pacific
            except Exception:
                continue
    return None


def _scan_sort_file_timefiltered(path, tail_n, lookback_minutes):
    lines = _tail_lines(path, tail_n)
    if not lines:
        return []

    now = datetime.now(TZ)
    cutoff = now - timedelta(minutes=lookback_minutes)

    filtered = []
    any_ts = False
    for i, line in enumerate(lines, 1):
        ts = _parse_ts_from_line(line)
        if ts is not None:
            any_ts = True
            if cutoff <= ts <= now:
                filtered.append((i, line))
    if not any_ts:
        # Fall back to scanning the last tail_n lines if no timestamps found
        filtered = [(i, line) for i, line in enumerate(lines, 1)]

    hits = []
    for i, line in filtered:
        if _has_error(line):
            hits.append(f"Line ~{i}: {line.strip()}")
    return hits


def _check_pickle_freshness(dir_path, filenames, minutes):
    """
    For each filename in dir_path, verify file exists and mtime >= now - minutes.
    Returns:
      (stale_or_missing, fresh_list) where
        stale_or_missing = list of (name, full_path, status_str, last_mtime_or_None)
        fresh_list = list of (name, full_path, mtime)
    """
    results_stale = []
    results_fresh = []
    cutoff = time.time() - minutes * 60
    for name in filenames:
        full = os.path.join(dir_path, name)
        if not os.path.exists(full):
            results_stale.append((name, full, "MISSING", None))
            continue
        try:
            mtime = os.path.getmtime(full)
        except OSError:
            results_stale.append((name, full, "UNREADABLE", None))
            continue
        if mtime >= cutoff:
            results_fresh.append((name, full, mtime))
        else:
            results_stale.append((name, full, "STALE", mtime))
    return results_stale, results_fresh


def _fmt_ts(ts_epoch):
    """Format epoch timestamp in Pacific time for display."""
    if ts_epoch is None:
        return "N/A"
    return datetime.fromtimestamp(ts_epoch, TZ).strftime("%Y-%m-%d %H:%M:%S %Z")


def run():
    findings, total_hits = [], 0

    # ---- 1) log checks ----
    if _file_exists_nonempty(PLP_1D_LOG):
        hits = _scan_simple_file(PLP_1D_LOG, TAIL_LINES_EAGER)
        if hits:
            findings.append(("plp_1d.log", PLP_1D_LOG, hits))
            total_hits += len(hits)
    if _file_exists_nonempty(PLP_20MIN_LOG):
        hits = _scan_simple_file(PLP_20MIN_LOG, TAIL_LINES_EAGER)
        if hits:
            findings.append(("plp_20min.log", PLP_20MIN_LOG, hits))
            total_hits += len(hits)
    if _file_exists_nonempty(PLP_SORT_LOG):
        hits = _scan_sort_file_timefiltered(PLP_SORT_LOG, SORT_TAIL_LINES, SORT_LOOKBACK_MINUTES)
        if hits:
            findings.append(("plp_sort_log.txt", PLP_SORT_LOG, hits))
            total_hits += len(hits)

    # ---- 2) pickle freshness ----
    stale_20, _ = _check_pickle_freshness(PICKLE_DIR, PICKLE_FILES_20min, PICKLE_FRESH_MINUTES_20min)
    stale_1d, _ = _check_pickle_freshness(PICKLE_DIR, PICKLE_FILES_1d, PICKLE_FRESH_MINUTES_1d)

    if not findings and not stale_20 and not stale_1d:
        print("No errors in logs and all pickle files are fresh.")
        return {"emailed": False}

    now_str = datetime.now(TZ).strftime("%Y-%m-%d %H:%M:%S %Z")
    lines = [f"Log/Asset Alert at {now_str}", ""]

    # Logs
    if findings:
        lines.append("=== Log Errors ===")
        for name, path, hits in findings:
            lines.append(f"File: {path} ({name})")
            for h in hits:
                lines.append("  " + h)
            lines.append("")
    else:
        lines.append("=== Log Errors ===\n  None detected.\n")

    # 20-min pickle set
    lines.append(f"=== Pickle Freshness ≤ {PICKLE_FRESH_MINUTES_20min} min (Pacific) ===")
    if stale_20:
        for n, f, s, m in stale_20:
            lines.append(f"  {s}: {n} ({f})  last_mtime={_fmt_ts(m)}")
    else:
        lines.append("  All 20-min pickle files are fresh.")
    lines.append("")

    # 1-day pickle set
    lines.append(f"=== Pickle Freshness ≤ {PICKLE_FRESH_MINUTES_1d} min (Pacific) ===")
    if stale_1d:
        for n, f, s, m in stale_1d:
            lines.append(f"  {s}: {n} ({f})  last_mtime={_fmt_ts(m)}")
    else:
        lines.append("  All 1-day pickle files are fresh.")
    lines.append("")

    body_text = "\n".join(lines)

    # Attach same body for convenience/search
    tmp = tempfile.NamedTemporaryFile(delete=False, suffix=".log", mode="w", encoding="utf-8")
    tmp.write(body_text + "\n")
    tmp.close()

    subject = (
        f"Alert (Pacific): {len(findings)} log error file(s); "
        f"{len(stale_20)} stale ≤ {PICKLE_FRESH_MINUTES_20min} min; "
        f"{len(stale_1d)} stale ≤ {PICKLE_FRESH_MINUTES_1d} min"
    )

    email_tools.send_text_email(
        sender=SENDER,
        receiver=RECEIVERS,
        email_text=body_text,
        email_subject=subject,
        attachment_paths=[tmp.name],
        attachment_names=["alert_details.log"],
    )

    print(subject)
    return {
        "emailed": True,
        "files_with_errors": len(findings),
        "total_error_hits": total_hits,
        "stale_pickle_files_20min": len(stale_20),
        "stale_pickle_files_1d": len(stale_1d),
    }


if __name__ == "__main__":
    run()
